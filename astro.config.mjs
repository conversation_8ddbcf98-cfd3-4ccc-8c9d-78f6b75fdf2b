// @ts-check
import { defineConfig, envField } from 'astro/config';

// https://astro.build/config
export default defineConfig({
  output: 'server',
  server: {
    allowedHosts: ["paykka-duty.liyujun.dev"]
  },
  env: {
    schema: {
      WECOM_CORP_ID: envField.string({ context: "client", access: "public", default: "" }),
      WECOM_CORP_SECRET: envField.string({ context: "server", access: "secret", default: "" }),
      WECOM_AGENT_ID: envField.string({ context: "client", access: "public", default: "" }),
    }
  }
});
